#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR图片文字识别脚本
支持多种OCR引擎：Tesseract, EasyOCR, PaddleOCR
"""

import cv2
import numpy as np
from PIL import Image
import argparse
import os
import sys

# 方法1: 使用Tesseract OCR
def ocr_with_tesseract(image_path, lang='chi_sim+eng'):
    """
    使用Tesseract进行OCR识别
    需要安装: pip install pytesseract pillow
    还需要安装Tesseract软件: https://github.com/tesseract-ocr/tesseract
    """
    try:
        import pytesseract
        
        # 读取图片
        image = Image.open(image_path)
        
        # 进行OCR识别
        text = pytesseract.image_to_string(image, lang=lang)
        
        return text.strip()
    except ImportError:
        print("请安装pytesseract: pip install pytesseract")
        return None
    except Exception as e:
        print(f"Tesseract OCR错误: {e}")
        return None

# 方法2: 使用EasyOCR
def ocr_with_easyocr(image_path, languages=['ch_sim', 'en']):
    """
    使用EasyOCR进行识别
    需要安装: pip install easyocr
    """
    try:
        import easyocr
        
        # 创建reader对象
        reader = easyocr.Reader(languages)
        
        # 进行OCR识别
        results = reader.readtext(image_path)
        
        # 提取文字
        text_list = []
        for (bbox, text, confidence) in results:
            if confidence > 0.5:  # 置信度阈值
                text_list.append(text)
        
        return '\n'.join(text_list)
    except ImportError:
        print("请安装easyocr: pip install easyocr")
        return None
    except Exception as e:
        print(f"EasyOCR错误: {e}")
        return None

# 方法3: 使用PaddleOCR
def ocr_with_paddleocr(image_path, lang='ch'):
    """
    使用PaddleOCR进行识别
    需要安装: pip install paddlepaddle paddleocr
    """
    try:
        from paddleocr import PaddleOCR
        
        # 创建OCR对象
        ocr = PaddleOCR(use_angle_cls=True, lang=lang)
        
        # 进行OCR识别
        results = ocr.ocr(image_path, cls=True)
        
        # 提取文字
        text_list = []
        for idx in range(len(results)):
            res = results[idx]
            if res:
                for line in res:
                    text_list.append(line[1][0])
        
        return '\n'.join(text_list)
    except ImportError:
        print("请安装paddleocr: pip install paddlepaddle paddleocr")
        return None
    except Exception as e:
        print(f"PaddleOCR错误: {e}")
        return None

# 图片预处理函数
def preprocess_image(image_path, output_path=None):
    """
    图片预处理，提高OCR识别准确率
    """
    try:
        # 读取图片
        img = cv2.imread(image_path)
        if img is None:
            print(f"无法读取图片: {image_path}")
            return None
        
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 降噪
        denoised = cv2.fastNlMeansDenoising(gray)
        
        # 二值化
        _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 形态学操作，去除噪点
        kernel = np.ones((1, 1), np.uint8)
        processed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        # 保存预处理后的图片
        if output_path:
            cv2.imwrite(output_path, processed)
            print(f"预处理后的图片已保存至: {output_path}")
        
        return processed
    except Exception as e:
        print(f"图片预处理错误: {e}")
        return None

def main():
    print('111')
    parser = argparse.ArgumentParser(description='OCR图片文字识别')
    parser.add_argument('image_path', help='输入图片路径')
    parser.add_argument('--method', choices=['tesseract', 'easyocr', 'paddleocr', 'all'], 
                       default='all', help='选择OCR方法')
    parser.add_argument('--preprocess', action='store_true', help='是否进行图片预处理')
    parser.add_argument('--output', help='输出文本文件路径')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.image_path):
        print(f"图片文件不存在: {args.image_path}")
        return
    
    print(f"正在识别图片: {args.image_path}")
    
    # 图片预处理
    processed_path = None
    if args.preprocess:
        processed_path = "processed_" + os.path.basename(args.image_path)
        preprocess_image(args.image_path, processed_path)
        image_to_ocr = processed_path
    else:
        image_to_ocr = args.image_path
    
    results = {}
    
    # 根据选择的方法进行OCR
    if args.method in ['tesseract', 'all']:
        print("\n--- 使用Tesseract OCR ---")
        text = ocr_with_tesseract(image_to_ocr)
        if text:
            results['Tesseract'] = text
            print(text)
    
    if args.method in ['easyocr', 'all']:
        print("\n--- 使用EasyOCR ---")
        text = ocr_with_easyocr(image_to_ocr)
        if text:
            results['EasyOCR'] = text
            print(text)
    
    if args.method in ['paddleocr', 'all']:
        print("\n--- 使用PaddleOCR ---")
        text = ocr_with_paddleocr(image_to_ocr)
        if text:
            results['PaddleOCR'] = text
            print(text)
    
    # 保存结果到文件
    if args.output and results:
        with open(args.output, 'w', encoding='utf-8') as f:
            for method, text in results.items():
                f.write(f"=== {method} ===\n")
                f.write(text)
                f.write("\n\n")
        print(f"\n识别结果已保存至: {args.output}")
    
    # 清理临时文件
    if processed_path and os.path.exists(processed_path):
        os.remove(processed_path)

# 简单的使用示例
def simple_ocr_example():
    """
    简单使用示例
    """
    image_path = "example.jpg"  # 替换为你的图片路径
    
    print("简单OCR示例:")
    
    # 使用EasyOCR（推荐，安装简单，效果好）
    print("使用EasyOCR识别:")
    text = ocr_with_easyocr(image_path)
    if text:
        print(text)

if __name__ == "__main__":
    # 如果直接运行脚本，使用命令行参数
    print('111222')
    if len(sys.argv) > 1:
        main()
    else:
        print("使用方法:")
        print("python ocr_script.py <图片路径> [选项]")
        print("\n选项:")
        print("  --method tesseract|easyocr|paddleocr|all  选择OCR方法")
        print("  --preprocess                              进行图片预处理")
        print("  --output <文件路径>                        保存结果到文件")
        print("\n示例:")
        print("python ocr_script.py image.jpg --method easyocr --preprocess --output result.txt")