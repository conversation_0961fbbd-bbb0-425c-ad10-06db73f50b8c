#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书评论机器人配置文件
"""

# 基础配置
CONFIG = {
    # 网站配置
    'base_url': 'https://xiaohongshu.com',
    
    # 页面元素选择器
    'selectors': {
        'search_input': 'input[id=search-input]',
        'note_item': '.note-item',
        'comment_input': '[contenteditable="true"]',
        'login_button_text': '登录',
    },
    
    # 时间配置（秒）
    'timing': {
        'comment_delay_min': 3,      # 评论间最小间隔
        'comment_delay_max': 8,      # 评论间最大间隔
        'page_load_delay': 2,        # 页面加载等待时间
        'login_check_interval': 5,   # 登录状态检查间隔
        'max_login_wait_time': 300,  # 最大登录等待时间（5分钟）
    },
    
    # 浏览器配置
    'browser': {
        'headless': False,           # 是否无头模式
        'width': 1280,              # 浏览器宽度
        'height': 720,              # 浏览器高度
        'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    },
    
    # 任务配置
    'task': {
        'default_keyword': '笑话',
        'default_comment': '有趣',  # 修改默认评论，避免与评论池第一个元素混淆
        'default_post_count': 20,
        'max_retry_count': 3,        # 最大重试次数
    },
    
    # 文件配置
    'files': {
        'cookie_file': 'xhs_cookies.json',
        'log_file': 'xhs_bot.log',
        'commented_posts_file': 'commented_posts.txt',  # 已评论帖子ID记录文件
    }
}

# 评论内容池（可随机选择）
COMMENT_POOL = [
    '直接用一键文生图wx小程序呀😆，输入文字直接可以转成文字图片',
    '推荐一键文生图wx小程序，别问我怎么知道的😌',
    'wx搜一键文生图小程序，直接把文字转成文字图片🤓'
]

# 搜索关键词池
KEYWORD_POOL = [
    '文字转图片'
]
