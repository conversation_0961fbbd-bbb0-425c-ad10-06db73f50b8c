#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书自动评论机器人 - 增强版
支持配置文件、随机评论内容、错误重试等功能
"""

import time
import random
import logging
import argparse
from typing import List, Optional
from playwright.sync_api import sync_playwright, Page, Browser
import json
import os
from config import CONFIG, COMMENT_POOL, KEYWORD_POOL

# 配置日志
def setup_logging(log_file: str = None):
    """设置日志配置"""
    log_file = log_file or CONFIG['files']['log_file']
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

class XHSCommentBotEnhanced:
    """小红书评论机器人增强版"""
    
    def __init__(self, config: dict = None):
        self.config = config or CONFIG
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.skip_comment_check = False  # 是否跳过重复评论检查
        self.commented_posts = set()  # 已评论的帖子ID集合

        # 统计信息
        self.stats = {
            'total_attempts': 0,
            'successful_comments': 0,
            'failed_comments': 0,
            'skipped_posts': 0,  # 跳过的帖子数量
            'start_time': None,
            'end_time': None,
        }

        # 加载已评论的帖子ID
        self.load_commented_posts()

    def load_commented_posts(self):
        """加载已评论的帖子ID"""
        try:
            commented_posts_file = self.config['files']['commented_posts_file']
            if os.path.exists(commented_posts_file):
                with open(commented_posts_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        post_id = line.strip()
                        if post_id:
                            self.commented_posts.add(post_id)
                logger.info(f"加载了 {len(self.commented_posts)} 个已评论帖子ID")
            else:
                logger.info("已评论帖子ID文件不存在，将创建新文件")
        except Exception as e:
            logger.error(f"加载已评论帖子ID失败: {e}")

    def save_commented_post(self, post_id: str):
        """保存已评论的帖子ID"""
        try:
            if post_id and post_id not in self.commented_posts:
                self.commented_posts.add(post_id)
                commented_posts_file = self.config['files']['commented_posts_file']
                with open(commented_posts_file, 'a', encoding='utf-8') as f:
                    f.write(f"{post_id}\n")
                logger.info(f"保存帖子ID: {post_id}")
        except Exception as e:
            logger.error(f"保存帖子ID失败: {e}")

    def get_current_post_id(self) -> Optional[str]:
        """从当前页面URL中提取帖子ID"""
        try:
            current_url = self.page.url
            # 小红书帖子URL格式: https://www.xiaohongshu.com/explore/{post_id}
            import re
            match = re.search(r'/explore/([a-f0-9]+)', current_url)
            if match:
                post_id = match.group(1)
                logger.info(f"提取到帖子ID: {post_id}")
                return post_id
            else:
                logger.warning(f"无法从URL中提取帖子ID: {current_url}")
                return None
        except Exception as e:
            logger.error(f"提取帖子ID失败: {e}")
            return None

    def is_post_commented(self, post_id: str) -> bool:
        """检查帖子是否已经评论过"""
        return post_id in self.commented_posts if post_id else False

    def setup_browser(self) -> bool:
        """初始化浏览器"""
        try:
            self.playwright = sync_playwright().start()
            browser_config = self.config['browser']
            
            self.browser = self.playwright.chromium.launch(
                headless=browser_config['headless'],
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            # 创建新页面
            context = self.browser.new_context(
                viewport={'width': browser_config['width'], 'height': browser_config['height']},
                user_agent=browser_config['user_agent']
            )
            self.page = context.new_page()
            
            logger.info("浏览器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"浏览器初始化失败: {e}")
            return False
    
    def save_cookies(self):
        """保存Cookie"""
        try:
            cookies = self.page.context.cookies()
            cookie_file = self.config['files']['cookie_file']
            with open(cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)
            logger.info("Cookie保存成功")
        except Exception as e:
            logger.error(f"Cookie保存失败: {e}")
    
    def clear_cookies(self):
        """清空Cookie文件"""
        try:
            cookie_file = self.config['files']['cookie_file']
            with open(cookie_file, 'w', encoding='utf-8') as f:
                json.dump([], f, ensure_ascii=False, indent=2)
            logger.info("Cookie文件已清空")
        except Exception as e:
            logger.error(f"清空Cookie文件失败: {e}")

    def load_cookies(self) -> bool:
        """加载Cookie"""
        try:
            cookie_file = self.config['files']['cookie_file']
            if os.path.exists(cookie_file):
                with open(cookie_file, 'r', encoding='utf-8') as f:
                    cookies = json.load(f)
                self.page.context.add_cookies(cookies)
                logger.info("Cookie加载成功")
                return True
        except Exception as e:
            logger.error(f"Cookie加载失败: {e}")
        return False
    
    def navigate_to_homepage(self) -> bool:
        """导航到小红书首页"""
        try:
            logger.info("正在访问小红书首页...")
            self.page.goto(self.config['base_url'], wait_until='networkidle')
            time.sleep(self.config['timing']['page_load_delay'])
            logger.info("成功访问小红书首页")
            return True
        except Exception as e:
            logger.error(f"访问首页失败: {e}")
            return False
    
    def check_login_status(self) -> bool:
        """检查登录状态"""
        try:
            # 等待页面加载完成
            time.sleep(1)

            # 使用JavaScript检查登录状态，更准确
            login_status = self.page.evaluate("""
                () => {
                    // 检查是否有登录按钮
                    const loginButtons = document.querySelectorAll('button, a, [class*="login"], [class*="Login"]');
                    let hasLoginButton = false;

                    for (let btn of loginButtons) {
                        const text = btn.textContent?.trim() || '';
                        if (text.includes('登录') || text.includes('登陆') || text.includes('Login')) {
                            hasLoginButton = true;
                            break;
                        }
                    }

                    // 检查是否有用户头像或用户信息
                    const userElements = document.querySelectorAll('.avatar, .user-info, [class*="user"], [class*="User"], [class*="profile"]');
                    const hasUserElements = userElements.length > 0;

                    // 检查URL是否包含登录相关路径
                    const isLoginPage = window.location.href.includes('login') || window.location.href.includes('signin');

                    // 检查是否有搜索框（登录后才会显示）
                    const searchInput = document.querySelector('input[id=search-input], [placeholder*="搜索"]');
                    const hasSearchInput = searchInput !== null;

                    return {
                        hasLoginButton: hasLoginButton,
                        hasUserElements: hasUserElements,
                        isLoginPage: isLoginPage,
                        hasSearchInput: hasSearchInput,
                        isLoggedIn: !hasLoginButton && !isLoginPage && (hasUserElements || hasSearchInput)
                    };
                }
            """)

            logger.info(f"登录状态检测: 登录按钮={login_status['hasLoginButton']}, 用户元素={login_status['hasUserElements']}, 搜索框={login_status['hasSearchInput']}")

            is_logged_in = login_status['isLoggedIn']

            if is_logged_in:
                logger.info("用户已登录")
            else:
                logger.info("用户未登录")

            return is_logged_in

        except Exception as e:
            logger.error(f"检查登录状态失败: {e}")
            return False
    
    def wait_for_login(self) -> bool:
        """等待用户登录"""
        logger.info("检测到未登录状态，请扫码登录...")

        # 尝试点击登录按钮，但不强制要求成功
        try:
            # 使用JavaScript查找并点击登录按钮
            login_clicked = self.page.evaluate("""
                () => {
                    const buttons = document.querySelectorAll('button, a, [class*="login"], [class*="Login"]');
                    for (let btn of buttons) {
                        const text = btn.textContent?.trim() || '';
                        if (text.includes('登录') || text.includes('登陆') || text.includes('Login')) {
                            btn.click();
                            return true;
                        }
                    }
                    return false;
                }
            """)

            if login_clicked:
                logger.info("已点击登录按钮，请使用小红书APP扫码登录")
                time.sleep(3)  # 等待登录页面加载
            else:
                logger.info("未找到登录按钮，请手动点击登录或刷新页面")

        except Exception as e:
            logger.warning(f"点击登录按钮失败: {e}")

        # 轮询检查登录状态，期间不进行任何页面操作
        timing = self.config['timing']
        start_time = time.time()
        check_count = 0

        while time.time() - start_time < timing['max_login_wait_time']:
            check_count += 1

            # 检查登录状态
            if self.check_login_status():
                logger.info("登录成功！")
                self.save_cookies()
                # 登录成功后等待一下，确保页面完全加载
                time.sleep(2)
                return True

            elapsed_time = int(time.time() - start_time)
            logger.info(f"等待登录中... ({elapsed_time}s) - 第{check_count}次检查")

            # 避免过于频繁的检查
            time.sleep(timing['login_check_interval'])

        logger.error("登录超时，请重试")
        return False
    
    def search_keyword(self, keyword: str) -> bool:
        """搜索关键词"""
        try:
            logger.info(f"正在搜索关键词: {keyword}")
            
            search_selector = self.config['selectors']['search_input']
            search_input = self.page.locator(search_selector)
            if search_input.count() == 0:
                logger.error("未找到搜索框")
                return False
            
            search_input.fill(keyword)
            time.sleep(1)
            search_input.press('Enter')
            
            time.sleep(self.config['timing']['page_load_delay'])
            logger.info("搜索完成")
            return True
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return False
    
    def get_post_count(self) -> int:
        """获取帖子数量"""
        try:
            note_selector = self.config['selectors']['note_item']
            return self.page.locator(note_selector).count()
        except:
            return 0

    def get_post_links_and_ids(self) -> List[dict]:
        """获取搜索结果页面中所有帖子的链接和ID

        Returns:
            List[dict]: 包含帖子信息的列表，每个元素包含 {'index': int, 'url': str, 'post_id': str}
        """
        try:
            note_selector = self.config['selectors']['note_item']

            # 使用JavaScript获取所有帖子的链接
            post_data = self.page.evaluate(f"""
                () => {{
                    const posts = document.querySelectorAll('{note_selector}');
                    const postData = [];

                    posts.forEach((post, index) => {{
                        // 查找帖子内的链接元素
                        const linkElement = post.querySelector('a[href*="/explore/"]');
                        if (linkElement) {{
                            const href = linkElement.href;
                            // 从URL中提取帖子ID
                            const match = href.match(/\\/explore\\/([a-f0-9]+)/);
                            if (match) {{
                                postData.push({{
                                    index: index,
                                    url: href,
                                    post_id: match[1]
                                }});
                            }}
                        }}
                    }});

                    return postData;
                }}
            """)

            logger.info(f"获取到 {len(post_data)} 个帖子的链接和ID")
            for post in post_data[:5]:  # 只显示前5个作为示例
                logger.info(f"帖子 {post['index'] + 1}: ID={post['post_id']}")

            return post_data

        except Exception as e:
            logger.error(f"获取帖子链接和ID失败: {e}")
            return []

    def filter_uncommented_posts(self, post_data: List[dict]) -> List[dict]:
        """过滤掉已评论的帖子

        Args:
            post_data: 包含帖子信息的列表

        Returns:
            List[dict]: 未评论的帖子列表
        """
        try:
            uncommented_posts = []
            commented_count = 0

            for post in post_data:
                post_id = post['post_id']
                if not self.is_post_commented(post_id):
                    uncommented_posts.append(post)
                else:
                    commented_count += 1
                    logger.debug(f"跳过已评论帖子: ID={post_id}, 索引={post['index'] + 1}")

            logger.info(f"过滤结果: 总帖子数={len(post_data)}, 已评论={commented_count}, 未评论={len(uncommented_posts)}")
            return uncommented_posts

        except Exception as e:
            logger.error(f"过滤帖子失败: {e}")
            return post_data

    def scroll_for_more_posts(self, target_count: int) -> int:
        """滚动页面获取更多帖子

        Args:
            target_count: 目标帖子数量

        Returns:
            int: 最终获取到的帖子数量
        """
        try:
            max_attempts = self.config['timing']['max_scroll_attempts']
            scroll_delay = self.config['timing']['scroll_delay']

            current_count = self.get_post_count()
            logger.info(f"开始滚动获取更多帖子，当前数量: {current_count}, 目标数量: {target_count}")

            no_new_posts_count = 0  # 连续没有新帖子的次数
            max_no_new_posts = 3    # 连续3次没有新帖子就停止

            for attempt in range(max_attempts):
                if current_count >= target_count:
                    logger.info(f"已获得足够的帖子数量: {current_count}")
                    break

                logger.info(f"第 {attempt + 1}/{max_attempts} 次滚动")

                # 滚动到页面底部
                self.page.evaluate("""
                    () => {
                        window.scrollTo(0, document.body.scrollHeight);
                    }
                """)

                # 等待新内容加载
                time.sleep(scroll_delay)

                # 检查帖子数量是否增加
                new_count = self.get_post_count()
                if new_count > current_count:
                    logger.info(f"滚动后帖子数量从 {current_count} 增加到 {new_count}")
                    current_count = new_count
                    no_new_posts_count = 0  # 重置计数器
                else:
                    no_new_posts_count += 1
                    logger.info(f"滚动后没有新帖子，连续 {no_new_posts_count} 次")

                    if no_new_posts_count >= max_no_new_posts:
                        logger.info("连续多次滚动都没有新帖子，停止滚动")
                        break

            logger.info(f"滚动完成，最终帖子数量: {current_count}")
            return current_count

        except Exception as e:
            logger.error(f"滚动获取帖子失败: {e}")
            return self.get_post_count()
    
    def click_post(self, index: int) -> bool:
        """点击指定索引的帖子"""
        try:
            note_selector = self.config['selectors']['note_item']
            posts = self.page.locator(note_selector)
            if posts.count() <= index:
                logger.error(f"帖子索引 {index} 超出范围")
                return False

            logger.info(f"正在点击第 {index + 1} 个帖子")
            posts.nth(index).click()
            time.sleep(self.config['timing']['page_load_delay'])
            return True

        except Exception as e:
            logger.error(f"点击帖子失败: {e}")
            return False

    def get_random_post_indices(self, total_posts: int, count: int) -> List[int]:
        """获取随机帖子索引列表"""
        if total_posts <= count:
            return list(range(total_posts))

        # 随机选择不重复的帖子索引
        indices = random.sample(range(total_posts), count)
        logger.info(f"随机选择的帖子索引: {[i+1 for i in indices]}")
        return indices

    def check_post_commented(self) -> bool:
        """检查当前帖子是否已经评论过（基于帖子ID）"""
        try:
            # 获取当前帖子ID
            post_id = self.get_current_post_id()
            if not post_id:
                logger.warning("无法获取帖子ID，默认允许评论")
                return False

            # 检查是否已经评论过
            if self.is_post_commented(post_id):
                logger.info(f"帖子 {post_id} 已经评论过，跳过")
                self.stats['skipped_posts'] += 1
                return True

            logger.info(f"帖子 {post_id} 未评论过，可以评论")
            return False

        except Exception as e:
            logger.warning(f"检查帖子评论状态失败: {e}，默认允许评论")
            return False
    
    def add_comment(self, comment_text: str) -> bool:
        """添加评论"""
        try:
            logger.info(f"正在添加评论: {comment_text}")
            
            comment_selector = self.config['selectors']['comment_input']
            
            # 使用JavaScript直接操作评论框
            result = self.page.evaluate(f"""
                () => {{
                    const editableElement = document.querySelector('{comment_selector}');
                    if (editableElement) {{
                        editableElement.focus();
                        editableElement.innerHTML = '{comment_text}';
                        editableElement.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        editableElement.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        return 'success';
                    }}
                    return 'no_input_found';
                }}
            """)
            
            if result != 'success':
                logger.error("未找到评论输入框")
                return False
            
            time.sleep(1)
            
            # 点击发送按钮
            send_result = self.page.evaluate("""
                () => {
                    const buttons = document.querySelectorAll('button');
                    for (let button of buttons) {
                        const text = button.textContent.trim();
                        if (text.includes('发送') || text.includes('提交') || text.includes('发布')) {
                            button.click();
                            return 'sent';
                        }
                    }
                    
                    const submitBtn = document.querySelector('button.btn.submit, button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.click();
                        return 'sent';
                    }
                    
                    return 'no_send_button';
                }
            """)
            
            if send_result == 'sent':
                logger.info("评论发送成功")
                self.stats['successful_comments'] += 1

                # 保存已评论的帖子ID
                post_id = self.get_current_post_id()
                if post_id:
                    self.save_commented_post(post_id)

                return True
            else:
                logger.error("未找到发送按钮")
                self.stats['failed_comments'] += 1
                return False
                
        except Exception as e:
            logger.error(f"添加评论失败: {e}")
            self.stats['failed_comments'] += 1
            return False
    
    def random_delay(self):
        """随机延时，模拟人类行为"""
        timing = self.config['timing']
        delay = random.uniform(timing['comment_delay_min'], timing['comment_delay_max'])
        logger.info(f"等待 {delay:.1f} 秒...")
        time.sleep(delay)
    
    def go_back(self) -> bool:
        """返回上一页"""
        try:
            self.page.go_back()
            time.sleep(self.config['timing']['page_load_delay'])
            return True
        except Exception as e:
            logger.error(f"返回上一页失败: {e}")
            return False
    
    def get_random_comment(self) -> str:
        """获取随机评论内容"""
        return random.choice(COMMENT_POOL)
    
    def get_random_keyword(self) -> str:
        """获取随机搜索关键词"""
        return random.choice(KEYWORD_POOL)
    
    def run_comment_task(self, keyword: str = None, comment_text: str = None,
                        post_count: int = None, use_random: bool = False) -> bool:
        """执行评论任务"""
        self.stats['start_time'] = time.time()

        try:
            # 0. 清空Cookie文件（每次启动时清空，不复用旧cookie）
            self.clear_cookies()

            # 使用默认值或随机值（默认使用随机评论）
            if not keyword:
                keyword = self.get_random_keyword() if use_random else self.config['task']['default_keyword']
            # comment_text 如果用户没有指定，则保持为 None，在循环中每次随机生成
            if not post_count:
                post_count = self.config['task']['default_post_count']

            if comment_text:
                logger.info(f"开始执行任务 - 关键词: {keyword}, 评论: {comment_text}, 帖子数: {post_count}")
            else:
                logger.info(f"开始执行任务 - 关键词: {keyword}, 评论: 随机生成, 帖子数: {post_count}")

            # 1. 初始化浏览器
            if not self.setup_browser():
                return False
            
            # 2. 加载Cookie
            self.load_cookies()
            
            # 3. 访问首页
            if not self.navigate_to_homepage():
                return False
            
            # 4. 检查登录状态
            if not self.check_login_status():
                if not self.wait_for_login():
                    return False
            
            # 5. 搜索关键词
            if not self.search_keyword(keyword):
                return False
            
            # 6. 获取帖子链接和ID，过滤已评论的帖子
            all_post_data = self.get_post_links_and_ids()
            if not all_post_data:
                logger.error("未能获取到任何帖子信息")
                return False

            logger.info(f"找到 {len(all_post_data)} 个帖子")

            # 过滤掉已评论的帖子
            uncommented_posts = self.filter_uncommented_posts(all_post_data)
            available_posts = len(uncommented_posts)

            logger.info(f"可评论的帖子数量: {available_posts}")

            # 如果可评论帖子数量不足，尝试滚动获取更多帖子
            if available_posts < post_count:
                logger.info(f"可评论帖子数量不足（{available_posts}/{post_count}），尝试滚动获取更多帖子")

                # 滚动获取更多帖子
                self.scroll_for_more_posts(post_count * 2)  # 滚动获取更多帖子

                # 重新获取帖子信息并过滤
                all_post_data = self.get_post_links_and_ids()
                uncommented_posts = self.filter_uncommented_posts(all_post_data)
                available_posts = len(uncommented_posts)

                logger.info(f"滚动后可评论的帖子数量: {available_posts}")

                # 滚动后仍然不足，调整目标数量
                if available_posts < post_count:
                    logger.warning(f"滚动后可评论帖子数量仍不足，将评论 {available_posts} 个帖子")
                    post_count = available_posts
                else:
                    logger.info(f"滚动后获得足够可评论帖子，将评论 {post_count} 个帖子")

            if available_posts == 0:
                logger.warning("没有可评论的帖子，任务结束")
                return False

            # 7. 随机选择未评论的帖子进行评论
            successful_comments = 0
            attempted_posts = set()  # 记录已尝试的帖子索引

            # 使用while循环，确保只有成功评论才计入目标数量
            while successful_comments < post_count and len(attempted_posts) < len(uncommented_posts):
                self.stats['total_attempts'] += 1
                logger.info(f"开始处理第 {successful_comments + 1}/{post_count} 个帖子")

                # 选择一个未尝试过的随机帖子
                remaining_posts = [i for i in range(len(uncommented_posts))
                                 if i not in attempted_posts]

                if not remaining_posts:
                    logger.warning("没有更多未尝试的帖子，结束任务")
                    break

                post_list_index = random.choice(remaining_posts)
                attempted_posts.add(post_list_index)

                selected_post = uncommented_posts[post_list_index]
                post_index = selected_post['index']  # 在页面中的实际索引
                post_id = selected_post['post_id']
                logger.info(f"尝试帖子索引: {post_index + 1}, ID: {post_id}")

                # 点击帖子
                if not self.click_post(post_index):
                    logger.warning(f"点击帖子 {post_index + 1} 失败，尝试下一个")
                    continue

                # 由于已经预先过滤了已评论的帖子，这里不需要再次检查
                # 直接进行评论
                logger.info(f"开始评论帖子: {post_index + 1}")

                # 生成评论内容：如果用户指定了评论内容则使用指定内容，否则每次随机生成
                if comment_text:
                    current_comment = comment_text  # 用户指定的固定评论
                else:
                    current_comment = self.get_random_comment()  # 默认每次随机生成

                # 添加评论
                if self.add_comment(current_comment):
                    logger.info(f"帖子 {post_index + 1} 评论成功")
                    successful_comments += 1
                    logger.info(f"已完成 {successful_comments}/{post_count} 个评论")
                else:
                    logger.warning(f"帖子 {post_index + 1} 评论失败")

                # 返回搜索结果页（除了最后一个）
                if successful_comments < post_count:
                    if not self.go_back():
                        logger.error("返回搜索页面失败")
                        break

                    # 随机延时
                    self.random_delay()

            # 任务完成统计
            logger.info(f"任务完成，成功评论 {successful_comments} 个帖子")

            self.stats['end_time'] = time.time()
            self.print_stats()
            
            return self.stats['successful_comments'] > 0
            
        except Exception as e:
            logger.error(f"执行任务失败: {e}")
            return False
        finally:
            self.cleanup()
    
    def print_stats(self):
        """打印统计信息"""
        duration = self.stats['end_time'] - self.stats['start_time']
        logger.info("=" * 50)
        logger.info("任务执行统计:")
        logger.info(f"总尝试次数: {self.stats['total_attempts']}")
        logger.info(f"成功评论: {self.stats['successful_comments']}")
        logger.info(f"失败评论: {self.stats['failed_comments']}")
        logger.info(f"跳过帖子: {self.stats['skipped_posts']} (已评论过)")
        logger.info(f"成功率: {self.stats['successful_comments']/max(self.stats['total_attempts'], 1)*100:.1f}%")
        logger.info(f"执行时间: {duration:.1f} 秒")
        logger.info(f"已记录帖子总数: {len(self.commented_posts)}")
        logger.info("=" * 50)
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.browser:
                self.browser.close()
            if self.playwright:
                self.playwright.stop()
            logger.info("资源清理完成")
        except Exception as e:
            logger.error(f"资源清理失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='小红书自动评论机器人')
    parser.add_argument('--keyword', '-k', type=str, help='搜索关键词')
    parser.add_argument('--comment', '-c', type=str, help='评论内容')
    parser.add_argument('--count', '-n', type=int, help='评论帖子数量')
    parser.add_argument('--random', '-r', action='store_true', help='使用随机关键词（评论内容默认已随机）')
    parser.add_argument('--headless', action='store_true', help='无头模式运行')
    parser.add_argument('--debug', '-d', action='store_true', help='调试模式，输出更多信息')
    parser.add_argument('--skip-comment-check', action='store_true', help='跳过重复评论检查')

    args = parser.parse_args()

    # 如果指定了无头模式，更新配置
    if args.headless:
        CONFIG['browser']['headless'] = True

    # 如果是调试模式，设置更详细的日志
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("调试模式已启用")

    # 创建机器人实例
    bot = XHSCommentBotEnhanced()
    bot.skip_comment_check = args.skip_comment_check  # 添加跳过检查选项

    # 执行评论任务
    success = bot.run_comment_task(
        keyword=args.keyword,
        comment_text=args.comment,
        post_count=args.count,
        use_random=args.random
    )

    if success:
        print("✅ 任务执行成功！")
    else:
        print("❌ 任务执行失败！")

if __name__ == "__main__":
    main()
